<div class="container light-mode-only">
  <div class="form-section">
    <h1 *ngIf="role === 'shopper'">Welcome to Shopper section</h1>
    <h1 *ngIf="role === 'seller'">Welcome to Seller section</h1>
    <h1 *ngIf="!role">Login</h1>
    <p>Don't have an account? <a [routerLink]="['/create-account']" [queryParams]="role ? {role: role} : {}">Create one</a></p>
    <form (ngSubmit)="onLogin()" #loginForm="ngForm">
      <input type="email" placeholder="Email" name="email" [(ngModel)]="loginData.email" required email>
      <div class="password-field">
        <input [type]="showPassword ? 'text' : 'password'" placeholder="Password" name="password" [(ngModel)]="loginData.password" required>
        <span class="toggle-password" (click)="togglePassword()">👁️</span>
      </div>
      <a routerLink="/forgot-password" class="forgot-password">Forgot Password?</a>
      <button type="submit" class="create-account-btn" [disabled]="!loginForm.valid">LOGIN</button>
    </form>
    <div class="divider">or</div>
    <button class="third-party google">
      <i class="fab fa-google"></i> Continue with Google
    </button>
    <button class="third-party apple">
      <i class="fab fa-apple"></i> Continue with Apple
    </button>
    <p class="error-message" *ngIf="errorMessage">{{ errorMessage }}</p>
    <footer>© 2023 Receeto ALL RIGHTS RESERVED.</footer>
  </div>
  <div class="right-section">
    <div class="receeto-logo"></div>
    <div class="bottom-nav">
      <a href="https://receeto.com" target="_blank" class="nav-link">Website</a>
      <a href="#" class="nav-link">Documentation</a>
      <a href="#" class="nav-link">Terms of Use</a>
      <a href="#" class="nav-link">Blog</a>
    </div>
  </div>